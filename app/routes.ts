import { type RouteConfig, index, route } from '@react-router/dev/routes';

const routes: RouteConfig = [
    index('routes/home.tsx'),
    route('about', 'routes/about.tsx'),
    route('domains', 'routes/domains/domains.tsx'),
    route('domains/:domain', 'routes/domains/domains.$domain.tsx'),
    route('vendors', 'routes/vendors.tsx'),
    route('auth/users', 'routes/auth/users.tsx'),
    route('auth/api-keys', 'routes/auth/api-keys.tsx'),
];

// Only add the .well-known route in development mode
if (process.env.NODE_ENV === 'development') {
    routes.push(route('.well-known/*', 'routes/.well-known.$.tsx'));
}

export default routes satisfies RouteConfig;
