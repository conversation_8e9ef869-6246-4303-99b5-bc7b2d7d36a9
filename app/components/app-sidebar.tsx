import { Link, useLocation } from 'react-router';

import {
    Activity,
    AtSign,
    BarChart3,
    Bell,
    CircleDollarSign,
    Earth,
    LayoutDashboard,
    RefreshCw,
    SlidersVertical,
    Sparkles,
} from 'lucide-react';

import {
    Sidebar,
    SidebarContent,
    SidebarGroup,
    SidebarGroupContent,
    SidebarGroupLabel,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
} from '~/components/ui/base';

// 导航数据
const navigationItems = [
    {
        title: '',
        items: [
            {
                title: '服务概览',
                url: '/',
                icon: LayoutDashboard,
            },
        ],
    },
    {
        title: '多云CDN管理',
        items: [
            {
                title: '多云域名',
                url: '/domains',
                icon: AtSign,
            },
            {
                title: '调度策略',
                url: '/scheduling',
                icon: SlidersVertical,
            },
            {
                title: '厂商管理',
                url: '/vendors',
                icon: Earth,
            },
        ],
    },
    {
        title: '数据中心',
        items: [
            {
                title: '成本中心',
                url: '/docs/dark-mode',
                icon: CircleDollarSign,
            },
            {
                title: '质量监控',
                url: '/data-center/quality',
                icon: Activity,
            },
            {
                title: '性能分析',
                url: '/data-center/performance',
                icon: BarChart3,
            },
        ],
    },

    {
        title: '运维中心',
        items: [
            {
                title: '刷新预热',
                url: '/coming-soon/refresh',
                icon: RefreshCw,
            },
            {
                title: '监控告警',
                url: '/coming-soon/monitoring',
                icon: Bell,
            },
            {
                title: '智能运维',
                url: '/coming-soon/ai-ops',
                icon: Sparkles,
            },
        ],
    },
];

export function AppSidebar() {
    const location = useLocation();

    return (
        <Sidebar collapsible='icon' className='pt-14'>
            <SidebarContent className='pt-4'>
                {navigationItems.map((group) => (
                    <SidebarGroup key={group.title || 'main'}>
                        {group.title && (
                            <SidebarGroupLabel>{group.title}</SidebarGroupLabel>
                        )}
                        <SidebarGroupContent>
                            <SidebarMenu>
                                {group.items.map((item) => {
                                    const Icon = item.icon;
                                    const isActive =
                                        location.pathname === item.url ||
                                        (item.url !== '/' &&
                                            location.pathname.startsWith(
                                                item.url + '/',
                                            ));

                                    return (
                                        <SidebarMenuItem key={item.title}>
                                            <SidebarMenuButton
                                                asChild
                                                isActive={isActive}
                                                className={
                                                    isActive
                                                        ? 'data-[active=true]:text-lilith-primary hover:bg-lilith-primary/10 text-sm data-[active=true]:bg-transparent data-[active=true]:font-medium'
                                                        : 'hover:bg-lilith-secondary/40 text-sm transition-colors'
                                                }
                                            >
                                                <Link to={item.url}>
                                                    <Icon
                                                        className={`h-4 w-4 ${!isActive ? 'text-muted-foreground' : ''}`}
                                                    />
                                                    <span>{item.title}</span>
                                                </Link>
                                            </SidebarMenuButton>
                                        </SidebarMenuItem>
                                    );
                                })}
                            </SidebarMenu>
                        </SidebarGroupContent>
                    </SidebarGroup>
                ))}
            </SidebarContent>
        </Sidebar>
    );
}
