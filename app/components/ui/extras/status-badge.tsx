import {
    AlertCircleIcon,
    CheckCircleIcon,
    CheckIcon,
    MinusCircleIcon,
    TriangleAlertIcon,
    ZapIcon,
    ZapOffIcon,
} from 'lucide-react';

import { Badge } from '~/components/ui/base';
import { cn } from '~/lib/utils';

// 支持的状态类型
export type StatusType =
    | 'active'
    | 'inactive'
    | 'error'
    | 'success'
    | 'warning'
    | 'pending';

// 状态配置接口
interface StatusConfig {
    icon: React.ComponentType<{ className?: string }>;
    color: string;
    border?: string;
    text: string;
}

// 默认状态配置
const defaultStatusConfigs: Record<StatusType, StatusConfig> = {
    active: {
        icon: ZapIcon,
        color: 'text-green-700 dark:text-green-600',
        text: '正常',
    },
    inactive: {
        icon: ZapOffIcon,
        color: 'text-lilith-muted dark:text-lilith-muted/50',
        text: '未激活',
    },
    error: {
        icon: TriangleAlertIcon,
        color: 'text-lilith-primary',
        text: '错误',
    },
    success: {
        icon: CheckCircleIcon,
        color: 'text-green-700',
        text: '成功',
    },
    warning: {
        icon: AlertCircleIcon,
        color: 'text-yellow-600',
        text: '警告',
    },
    pending: {
        icon: ZapOffIcon,
        color: 'text-blue-600',
        text: '等待中',
    },
};

interface StatusBadgeProps {
    /** 状态值 */
    status: string;
    /** 状态类型，用于确定默认的图标和样式 */
    type?: StatusType;
    /** 自定义状态配置，会覆盖默认配置 */
    customConfigs?: Partial<Record<string, StatusConfig>>;
    /** 自定义文本映射函数 */
    getStatusText?: (status: string) => string;
    /** 额外的 CSS 类名 */
    className?: string;
    /** 是否显示图标 */
    showIcon?: boolean;
    /** 图标大小 */
    iconSize?: string;
}

export function StatusBadge({
    status,
    type,
    customConfigs = {},
    getStatusText,
    className,
    showIcon = true,
    iconSize = 'h-3 w-3',
}: StatusBadgeProps) {
    // 优先使用自定义配置，然后是根据 type 的默认配置，最后是通用配置
    const getConfig = (): StatusConfig => {
        if (customConfigs[status]) {
            return {
                ...defaultStatusConfigs[type || 'active'],
                ...customConfigs[status],
            } as StatusConfig;
        }

        if (type && defaultStatusConfigs[type]) {
            return defaultStatusConfigs[type];
        }

        if (defaultStatusConfigs[status as StatusType]) {
            return defaultStatusConfigs[status as StatusType];
        }

        return {
            icon: AlertCircleIcon,
            color: 'text-gray-700',
            border: 'border-gray-200',
            text: status,
        };
    };

    const config = getConfig();
    const IconComponent = config.icon;

    const displayText = getStatusText ? getStatusText(status) : config.text;

    return (
        <Badge
            variant='outline'
            className={cn(
                'bg-background text-xs font-normal',
                config.color,
                config.border,
                className,
            )}
        >
            {showIcon && <IconComponent className={iconSize} />}
            {displayText}
        </Badge>
    );
}

export function DomainStatusBadge({
    status,
    getStatusText,
    className,
}: {
    status: string;
    getStatusText?: (status: string) => string;
    className?: string;
}) {
    return (
        <StatusBadge
            status={status}
            getStatusText={getStatusText}
            className={className}
        />
    );
}

export function VendorStatusBadge({
    status,
    getStatusText,
    className,
}: {
    status: string;
    getStatusText?: (status: string) => string;
    className?: string;
}) {
    const defaultGetStatusText = (status: string) => {
        switch (status) {
            case 'active':
                return '正常';
            case 'inactive':
                return '未激活';
            case 'error':
                return '错误';
            default:
                return '未知';
        }
    };

    const defaultConfigs: Record<string, StatusConfig> = {
        active: {
            icon: CheckIcon,
            color: 'text-green-700',
            text: '正常',
        },
        inactive: {
            icon: MinusCircleIcon,
            color: 'text-lilith-muted',
            text: '未激活',
        },
    };

    return (
        <StatusBadge
            status={status}
            getStatusText={getStatusText || defaultGetStatusText}
            className={className}
            customConfigs={defaultConfigs}
        />
    );
}
