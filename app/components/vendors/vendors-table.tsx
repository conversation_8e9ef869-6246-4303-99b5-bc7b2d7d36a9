import * as React from 'react';

import {
    type ColumnDef,
    type ColumnFiltersState,
    type SortingState,
    type VisibilityState,
    flexRender,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    getSortedRowModel,
    useReactTable,
} from '@tanstack/react-table';
import {
    ArrowUpDown,
    HelpCircle,
    MoreHorizontal,
    Pencil,
    RotateCcw,
    Trash2,
} from 'lucide-react';

import { CloudIconWithText, VendorStatusBadge } from '~/components/ui';
import {
    Button,
    Checkbox,
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
    Switch,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
    Tooltip,
    TooltipContent,
    TooltipTrigger,
} from '~/components/ui/base';
import { type CloudProvider } from '~/types/cloud';

// 厂商账号类型定义
export interface VendorAccount {
    id: string;
    accountName: string;
    accountId: string;
    provider: CloudProvider;
    product: string;
    readOnlyMode: boolean;
    lastSyncTime: string;
    status: 'active' | 'inactive' | 'error';
    domains: number;
}

interface VendorsTableProps {
    accounts: VendorAccount[];
    onToggleReadOnlyMode?: (accountId: string) => void;
    onEdit?: (account: VendorAccount) => void;
    onDelete?: (accountId: string) => void;
    onRefresh?: (accountId: string) => void;
}

const getStatusText = (status: string) => {
    switch (status) {
        case 'active':
            return '正常';
        case 'inactive':
            return '未激活';
        case 'error':
            return '错误';
        default:
            return '未知';
    }
};

// 列定义
export function getColumns(
    onToggleReadOnlyMode?: (accountId: string) => void,
    onEdit?: (account: VendorAccount) => void,
    onDelete?: (accountId: string) => void,
    onRefresh?: (accountId: string) => void,
): ColumnDef<VendorAccount>[] {
    return [
        {
            id: 'select',
            header: ({ table }) => (
                <Checkbox
                    checked={
                        table.getIsAllPageRowsSelected() ||
                        (table.getIsSomePageRowsSelected() && 'indeterminate')
                    }
                    onCheckedChange={(value) =>
                        table.toggleAllPageRowsSelected(!!value)
                    }
                    aria-label='选择全部'
                />
            ),
            cell: ({ row }) => (
                <Checkbox
                    checked={row.getIsSelected()}
                    onCheckedChange={(value) => row.toggleSelected(!!value)}
                    aria-label='选择行'
                />
            ),
            enableSorting: false,
            enableHiding: false,
        },
        {
            accessorKey: 'accountName',
            header: ({ column }) => {
                return (
                    <Button
                        variant='ghost'
                        onClick={() =>
                            column.toggleSorting(column.getIsSorted() === 'asc')
                        }
                        className='text-xs font-medium'
                    >
                        账号信息
                        <ArrowUpDown className='ml-2 h-4 w-4' />
                    </Button>
                );
            },
            cell: ({ row }) => (
                <div>
                    <div className='text-xs font-normal'>
                        {row.getValue('accountName')}
                    </div>
                    <div className='text-muted-foreground text-xs'>
                        {row.original.accountId}
                    </div>
                </div>
            ),
        },
        {
            accessorKey: 'provider',
            header: '云服务商',
            cell: ({ row }) => {
                return (
                    <div className='flex items-center gap-2'>
                        <CloudIconWithText
                            provider={row.getValue('provider')}
                            size={16}
                        />
                    </div>
                );
            },
        },
        {
            accessorKey: 'product',
            header: '产品',
            cell: ({ row }) => (
                <div className='text-xs lowercase'>
                    {row.getValue('product')}
                </div>
            ),
        },
        {
            accessorKey: 'domains',
            header: ({ column }) => {
                return (
                    <Button
                        variant='ghost'
                        onClick={() =>
                            column.toggleSorting(column.getIsSorted() === 'asc')
                        }
                        className='text-xs font-medium'
                    >
                        域名数量
                        <ArrowUpDown className='ml-2 h-4 w-4' />
                    </Button>
                );
            },
            cell: ({ row }) => (
                <div className='text-center text-xs font-normal'>
                    {row.getValue('domains')}
                </div>
            ),
        },
        {
            accessorKey: 'readOnlyMode',
            header: () => (
                <div className='flex items-center gap-1'>
                    只读模式
                    <Tooltip>
                        <TooltipTrigger asChild>
                            <HelpCircle className='text-muted-foreground size-4 cursor-pointer' />
                        </TooltipTrigger>
                        <TooltipContent>
                            <p>
                                开启后，该云账号只会进行从云服务商处同步数据，无法向云服务商执行写操作，如新建、删除或修改等
                            </p>
                        </TooltipContent>
                    </Tooltip>
                </div>
            ),
            cell: ({ row }) => {
                const account = row.original;
                return (
                    <Switch
                        checked={account.readOnlyMode}
                        onCheckedChange={() =>
                            onToggleReadOnlyMode?.(account.id)
                        }
                        className='h-[0.875rem] w-6'
                    />
                );
            },
        },
        {
            accessorKey: 'status',
            header: '状态',
            cell: ({ row }) => {
                const status = row.getValue('status') as string;
                return (
                    <VendorStatusBadge
                        status={status}
                        getStatusText={getStatusText}
                    />
                );
            },
            filterFn: (row, id, value) => {
                return value.includes(row.getValue(id));
            },
        },
        {
            accessorKey: 'lastSyncTime',
            header: ({ column }) => {
                return (
                    <Button
                        variant='ghost'
                        onClick={() =>
                            column.toggleSorting(column.getIsSorted() === 'asc')
                        }
                        className='text-xs font-medium'
                    >
                        最后同步
                        <ArrowUpDown className='ml-2 h-4 w-4' />
                    </Button>
                );
            },
            cell: ({ row }) => (
                <div className='text-muted-foreground text-xs'>
                    {row.getValue('lastSyncTime')}
                </div>
            ),
        },
        {
            id: 'actions',
            enableHiding: false,
            cell: ({ row }) => {
                const account = row.original;

                return (
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant='ghost' className='h-8 w-8 p-0'>
                                <span className='sr-only'>打开菜单</span>
                                <MoreHorizontal className='h-4 w-4' />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align='end'>
                            <DropdownMenuLabel>操作</DropdownMenuLabel>
                            <DropdownMenuItem
                                onClick={() =>
                                    navigator.clipboard.writeText(account.id)
                                }
                            >
                                复制账号ID
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => onEdit?.(account)}>
                                <Pencil className='mr-2 h-4 w-4' />
                                编辑
                            </DropdownMenuItem>
                            <DropdownMenuItem
                                onClick={() => onRefresh?.(account.id)}
                            >
                                <RotateCcw className='mr-2 h-4 w-4' />
                                刷新
                            </DropdownMenuItem>
                            <DropdownMenuItem
                                onClick={() => onDelete?.(account.id)}
                            >
                                <Trash2 className='mr-2 h-4 w-4' />
                                删除
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                );
            },
        },
    ];
}

export function VendorsTable({
    accounts,
    onToggleReadOnlyMode,
    onEdit,
    onDelete,
    onRefresh,
}: VendorsTableProps) {
    const [sorting, setSorting] = React.useState<SortingState>([]);
    const [columnFilters, setColumnFilters] =
        React.useState<ColumnFiltersState>([]);
    const [columnVisibility, setColumnVisibility] =
        React.useState<VisibilityState>({});
    const [rowSelection, setRowSelection] = React.useState({});

    const columns = React.useMemo(
        () => getColumns(onToggleReadOnlyMode, onEdit, onDelete, onRefresh),
        [onToggleReadOnlyMode, onEdit, onDelete, onRefresh],
    );

    const table = useReactTable({
        data: accounts,
        columns,
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getSortedRowModel: getSortedRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        onColumnVisibilityChange: setColumnVisibility,
        onRowSelectionChange: setRowSelection,
        state: {
            sorting,
            columnFilters,
            columnVisibility,
            rowSelection,
        },
    });

    return (
        <div className='w-full'>
            <div className='rounded-sm border'>
                <Table>
                    <TableHeader className='bg-lilith-muted/10'>
                        {table.getHeaderGroups().map((headerGroup) => (
                            <TableRow key={headerGroup.id}>
                                {headerGroup.headers.map((header) => {
                                    return (
                                        <TableHead
                                            key={header.id}
                                            className='text-xs'
                                        >
                                            {header.isPlaceholder
                                                ? null
                                                : flexRender(
                                                      header.column.columnDef
                                                          .header,
                                                      header.getContext(),
                                                  )}
                                        </TableHead>
                                    );
                                })}
                            </TableRow>
                        ))}
                    </TableHeader>
                    <TableBody>
                        {table.getRowModel().rows?.length ? (
                            table.getRowModel().rows.map((row) => (
                                <TableRow
                                    key={row.id}
                                    data-state={
                                        row.getIsSelected() && 'selected'
                                    }
                                >
                                    {row.getVisibleCells().map((cell) => (
                                        <TableCell key={cell.id}>
                                            {flexRender(
                                                cell.column.columnDef.cell,
                                                cell.getContext(),
                                            )}
                                        </TableCell>
                                    ))}
                                </TableRow>
                            ))
                        ) : (
                            <TableRow>
                                <TableCell
                                    colSpan={columns.length}
                                    className='h-24 text-center'
                                >
                                    没有结果
                                </TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </div>
            <div className='flex items-center justify-end space-x-2 py-4'>
                <div className='text-muted-foreground flex-1 text-sm'>
                    已选择 {table.getFilteredSelectedRowModel().rows.length}{' '}
                    行，共 {table.getFilteredRowModel().rows.length} 行。
                </div>
                <div className='space-x-2'>
                    <Button
                        variant='outline'
                        size='sm'
                        onClick={() => table.previousPage()}
                        disabled={!table.getCanPreviousPage()}
                    >
                        上一页
                    </Button>
                    <Button
                        variant='outline'
                        size='sm'
                        onClick={() => table.nextPage()}
                        disabled={!table.getCanNextPage()}
                    >
                        下一页
                    </Button>
                </div>
            </div>
        </div>
    );
}
