import { useState } from 'react';

import { AlertCircleIcon, Plus, Search } from 'lucide-react';

import { Alert, AlertDescription, Button, Input } from '~/components/ui/base';
import {
    VendorsTable,
    type VendorAccount,
} from '~/components/vendors/vendors-table';

// 模拟数据
const mockVendorAccounts: VendorAccount[] = [
    {
        id: '1',
        accountName: 'lilith-ali-primary',
        accountId: '6880b7337ad7caae44abade2',
        provider: 'aliyun',
        product: 'CDN',
        readOnlyMode: false,
        lastSyncTime: '2024-01-28 10:30',
        status: 'active',
        domains: 15,
    },
    {
        id: '2',
        accountName: 'lilith-tencent-backup',
        accountId: '7991c8448be8dbbf55cbcdf3',
        provider: 'tencent',
        product: 'CDN',
        readOnlyMode: true,
        lastSyncTime: '2024-01-28 09:45',
        status: 'active',
        domains: 8,
    },
    {
        id: '3',
        accountName: 'lilith-aws-main',
        accountId: 'aws-************',
        provider: 'aws',
        product: 'CDN',
        readOnlyMode: false,
        lastSyncTime: '2024-01-28 11:15',
        status: 'error',
        domains: 23,
    },
    {
        id: '4',
        accountName: 'lilith-akamai-prod',
        accountId: 'akamai-subscription-001',
        provider: 'akamai',
        product: 'CDN',
        readOnlyMode: true,
        lastSyncTime: '2024-01-27 16:20',
        status: 'inactive',
        domains: 5,
    },
    {
        id: '5',
        accountName: 'lilith-tencent-dev',
        accountId: 'volc-dev-account-002',
        provider: 'volcengine',
        product: 'CDN',
        readOnlyMode: false,
        lastSyncTime: '2024-01-28 08:30',
        status: 'active',
        domains: 12,
    },
];

export default function VendorsPage() {
    const [vendorAccounts, setVendorAccounts] =
        useState<VendorAccount[]>(mockVendorAccounts);
    const [searchTerm, setSearchTerm] = useState('');
    const [_selectedAccount, _setSelectedAccount] =
        useState<VendorAccount | null>(null);

    const filteredAccounts = vendorAccounts.filter(
        (account) =>
            account.accountName
                .toLowerCase()
                .includes(searchTerm.toLowerCase()) ||
            account.accountId.toLowerCase().includes(searchTerm.toLowerCase()),
    );

    const handleToggleReadOnlyMode = (accountId: string) => {
        setVendorAccounts((accounts) =>
            accounts.map((account) =>
                account.id === accountId
                    ? { ...account, readOnlyMode: !account.readOnlyMode }
                    : account,
            ),
        );
    };

    const handleEdit = (account: VendorAccount) => {
        _setSelectedAccount(account);
        // 这里可以打开编辑对话框
        // TODO: 实现编辑功能
    };

    const handleDelete = (accountId: string) => {
        if (confirm('确定要删除这个账号吗？')) {
            setVendorAccounts((accounts) =>
                accounts.filter((account) => account.id !== accountId),
            );
        }
    };

    const handleRefresh = (accountId: string) => {
        // 这里可以实现刷新逻辑
        // TODO: 实现从云服务商同步数据的功能
        // 更新最后同步时间
        setVendorAccounts((accounts) =>
            accounts.map((account) =>
                account.id === accountId
                    ? {
                          ...account,
                          lastSyncTime: new Date()
                              .toLocaleString('zh-CN', {
                                  year: 'numeric',
                                  month: '2-digit',
                                  day: '2-digit',
                                  hour: '2-digit',
                                  minute: '2-digit',
                              })
                              .replace(/\//g, '-'),
                      }
                    : account,
            ),
        );
    };

    return (
        <div className='space-y-6'>
            <div className='space-y-2'>
                <h1 className='text-3xl font-bold tracking-tight'>厂商管理</h1>
                <Alert variant='default'>
                    <AlertCircleIcon />
                    <AlertDescription>
                        管理多云服务商账号，同步加速域名，实现统一的多云CDN管理
                    </AlertDescription>
                </Alert>
            </div>

            {/* 厂商账号管理 */}
            <div className='mb-6 flex items-center justify-end'>
                <div className='flex items-center gap-3'>
                    <div className='relative'>
                        <Search className='absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400' />
                        <Input
                            placeholder='搜索账号名称'
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className='w-64 pl-10'
                        />
                    </div>
                    <Button className='flex items-center gap-2 rounded-md px-4 py-2 transition-colors duration-200'>
                        <Plus className='h-4 w-4' />
                        添加账号
                    </Button>
                </div>
            </div>

            {/* 表格 */}
            <VendorsTable
                accounts={filteredAccounts}
                onToggleReadOnlyMode={handleToggleReadOnlyMode}
                onEdit={handleEdit}
                onDelete={handleDelete}
                onRefresh={handleRefresh}
            />
        </div>
    );
}
