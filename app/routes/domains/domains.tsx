import { useState } from 'react';

import { AlertCircleIcon, PlusIcon, Search } from 'lucide-react';

import { DomainsTable, type Domain } from '~/components/domains/table';
import {
    Alert,
    AlertDescription,
    Badge,
    Button,
    Input,
} from '~/components/ui/base';
import type { CloudProvider } from '~/types/cloud';

export function meta() {
    return [{ title: '域名管理' }];
}

// 模拟域名数据
const mockDomains: Domain[] = [
    {
        id: '1',
        domain: 'b4-official.lilith.com',
        trafficScheduling: 'enabled',
        provider: 'volcengine',
        product: 'CDN',
        status: 'active',
        accelerationType: 'web',
        accelerationRegion: 'china',
        httpsStatus: 'disabled',
        tags: [],
        projectGroup: 'default',
        lastUpdateTime: '3 分钟前',
    },
    {
        id: '2',
        domain: 'farlight84-beta.farlightgame.com',
        trafficScheduling: 'disabled',
        provider: 'aliyun',
        product: 'CDN',
        status: 'active',
        accelerationType: 'download',
        accelerationRegion: 'global',
        httpsStatus: 'enabled',
        tags: [],
        projectGroup: 'default',
        lastUpdateTime: '5 分钟前',
    },
    {
        id: '3',
        domain: 'activity-yok.gamota.com',
        trafficScheduling: 'disabled',
        provider: 'aliyun',
        product: 'CDN',
        status: 'active',
        accelerationType: 'web',
        accelerationRegion: 'global',
        httpsStatus: 'enabled',
        tags: [],
        projectGroup: 'default',
        lastUpdateTime: '5 分钟前',
    },
    {
        id: '4',
        domain: 'front-test.lilithcdn.com',
        trafficScheduling: 'disabled',
        provider: 'aliyun',
        product: 'CDN',
        status: 'active',
        accelerationType: 'web',
        accelerationRegion: 'china',
        httpsStatus: 'enabled',
        tags: [],
        projectGroup: 'default',
        lastUpdateTime: '5 分钟前',
    },
    {
        id: '5',
        domain: 'cnocdtv-bisai.lilithgame.com',
        trafficScheduling: 'disabled',
        provider: 'aliyun',
        product: 'CDN',
        status: 'active',
        accelerationType: 'download',
        accelerationRegion: 'china',
        httpsStatus: 'enabled',
        tags: [],
        projectGroup: 'default',
        lastUpdateTime: '5 分钟前',
    },
    {
        id: '6',
        domain: 'www.simplegames.sg',
        trafficScheduling: 'disabled',
        provider: 'aliyun',
        product: 'CDN',
        status: 'active',
        accelerationType: 'web',
        accelerationRegion: 'global',
        httpsStatus: 'enabled',
        tags: [],
        projectGroup: 'default',
        lastUpdateTime: '5 分钟前',
    },
    {
        id: '7',
        domain: 'www.miraclegames.sg',
        trafficScheduling: 'disabled',
        provider: 'aliyun',
        product: 'CDN',
        status: 'inactive',
        accelerationType: 'web',
        accelerationRegion: 'global',
        httpsStatus: 'enabled',
        tags: [],
        projectGroup: 'default',
        lastUpdateTime: '5 分钟前',
    },
    {
        id: '8',
        domain: 'www.lilith.com',
        trafficScheduling: 'disabled',
        provider: 'akamai',
        product: 'CDN',
        status: 'active',
        accelerationType: 'web',
        accelerationRegion: 'global',
        httpsStatus: 'enabled',
        tags: [],
        projectGroup: 'default',
        lastUpdateTime: '5 分钟前',
    },
    {
        id: '9',
        domain: 'lilith.sh',
        trafficScheduling: 'disabled',
        provider: 'aliyun',
        product: 'CDN',
        status: 'error',
        accelerationType: 'web',
        accelerationRegion: 'global',
        httpsStatus: 'enabled',
        tags: [],
        projectGroup: 'default',
        lastUpdateTime: '5 分钟前',
    },
    {
        id: '10',
        domain: 'www.lilith.sh',
        trafficScheduling: 'disabled',
        provider: 'aliyun',
        product: 'CDN',
        status: 'active',
        accelerationType: 'web',
        accelerationRegion: 'global',
        httpsStatus: 'enabled',
        tags: [],
        projectGroup: 'default',
        lastUpdateTime: '5 分钟前',
    },
];

export default function DomainsPage() {
    const [domains, setDomains] = useState<Domain[]>(mockDomains);
    const [isLoading, setIsLoading] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedProvider, setSelectedProvider] = useState<
        CloudProvider | 'all'
    >('all');
    const [selectedStatus, setSelectedStatus] = useState<string>('all');

    // 过滤域名
    const filteredDomains = domains.filter((domain) => {
        const matchesSearch = domain.domain
            .toLowerCase()
            .includes(searchTerm.toLowerCase());
        const matchesProvider =
            selectedProvider === 'all' || domain.provider === selectedProvider;
        const matchesStatus =
            selectedStatus === 'all' || domain.status === selectedStatus;

        return matchesSearch && matchesProvider && matchesStatus;
    });

    // const handleToggleTrafficScheduling = (domainId: string) => {
    //     setDomains((prevDomains) =>
    //         prevDomains.map((domain) =>
    //             domain.id === domainId
    //                 ? {
    //                       ...domain,
    //                       trafficScheduling:
    //                           domain.trafficScheduling === 'enabled'
    //                               ? 'disabled'
    //                               : 'enabled',
    //                   }
    //                 : domain,
    //         ),
    //     );
    // };

    const handleEdit = (domain: Domain) => {
        // TODO: 实现编辑功能
        // eslint-disable-next-line no-console
        console.log('编辑域名:', domain);
    };

    const handleDelete = (domainId: string) => {
        if (confirm('确定要删除这个域名吗？')) {
            setDomains((prevDomains) =>
                prevDomains.filter((domain) => domain.id !== domainId),
            );
        }
    };

    // const handleRefresh = (domainId: string) => {
    //     // TODO: 实现刷新功能
    //     setDomains((prevDomains) =>
    //         prevDomains.map((domain) =>
    //             domain.id === domainId
    //                 ? {
    //                       ...domain,
    //                       lastUpdateTime: '刚刚',
    //                   }
    //                 : domain,
    //         ),
    //     );
    // };

    return (
        <div className='space-y-6'>
            {/* 页面标题 */}
            <div className='space-y-4'>
                <h1 className='text-3xl font-bold tracking-tight'>域名管理</h1>
                <Alert variant='default'>
                    <AlertCircleIcon />
                    <AlertDescription>
                        多云管理助您根据业务机制，聚合您所有的服务商资源。
                    </AlertDescription>
                </Alert>
            </div>

            {/* 操作栏 */}
            <div className='flex items-center justify-between'>
                <div className='flex items-center gap-4'>
                    {/* 筛选器 */}
                    <div className='flex items-center gap-2'>
                        {/* 状态筛选 */}
                        {selectedStatus !== 'all' && (
                            <Badge variant='secondary' className='text-xs'>
                                状态: {selectedStatus}
                                <button
                                    onClick={() => setSelectedStatus('all')}
                                    className='hover:text-lilith-primary ml-1 text-xs'
                                >
                                    ×
                                </button>
                            </Badge>
                        )}

                        {/* 云服务商筛选 */}
                        {selectedProvider !== 'all' && (
                            <Badge variant='secondary' className='text-xs'>
                                云服务商: {selectedProvider}
                                <button
                                    onClick={() => setSelectedProvider('all')}
                                    className='hover:text-lilith-primary ml-1 text-xs'
                                >
                                    ×
                                </button>
                            </Badge>
                        )}
                    </div>
                </div>
            </div>

            {/* 域名列表 */}
            <div className='space-y-4'>
                {/* 标签页 */}
                <div className='flex items-center justify-end gap-6'>
                    <div className='relative'>
                        <Search className='absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400' />
                        <Input
                            placeholder='搜索域名'
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className='w-64 pl-10'
                        />
                    </div>

                    <div className='flex gap-2'>
                        <Button
                            size='sm'
                            variant='outline'
                            onClick={() => {
                                setIsLoading(true);
                                setTimeout(() => setIsLoading(false), 2000);
                            }}
                        >
                            {isLoading ? '加载中...' : '模拟加载数据'}
                        </Button>

                        <Button size='sm' variant='outline'>
                            <PlusIcon /> 向云服务商添加域名
                        </Button>
                    </div>
                </div>

                {/* 表格 */}
                <DomainsTable
                    domains={filteredDomains}
                    isLoading={isLoading}
                    onEdit={handleEdit}
                    onDelete={handleDelete}
                />

                {/* 统计信息 */}
                <div className='text-muted-foreground text-sm'>
                    共 {filteredDomains.length} 条
                </div>
            </div>
        </div>
    );
}
